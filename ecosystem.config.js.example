module.exports = {
  apps: [
    {
      name: 'sds-context-api',
      exec_mode: 'fork',
      instances: 1,
      cron_restart: '0 */2 * * *',
      interpreter: '/home/<USER>/.local/share/fnm/fnm',
      interpreter_args: 'exec --using=22.19.0 node',
      script: 'npm',
      args: 'run start',
      watch: false
    },
    {
      name: "sds-context-worker",
      exec_mode: "cluster",
      instances: 2,
      interpreter: "/home/<USER>/.local/share/fnm/aliases/22.17.0/bin/node",
      script: "./dist/bull/Worker.js",
      watch: false
    }
  ]
}
