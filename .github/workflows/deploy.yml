# This is a basic workflow to help you get started with Actions

name: Deploy

# Controls when the action will run.
on:
  push:
    branches: [ main ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  deploy:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: appleboy/ssh-action@v1.0.3
        with:
          # ssh host
          host: orc1.sds.tec.br
          # ssh username
          username: sds
          # content of ssh private key. ex raw content of ~/.ssh/id_rsa
          key: '${{ secrets.SDS_ORC1_SRV }}'
          # execute commands
          script: |
            FNM_PATH="/home/<USER>/.local/share/fnm"
            if [ -d "$FNM_PATH" ]; then
              export PATH="/home/<USER>/.local/share/fnm:$PATH"
              eval "`fnm env`"
              fnm use 22.19.0
            fi

            cd /home/<USER>/services/sds-context-api
            git pull

            npm install
            npm run build:ts

            fnm use default
            FORCE_COLOR=1 pm2 restart ecosystem.config.cjs --time
          # stop script after first failure
          script_stop: true