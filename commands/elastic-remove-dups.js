import elasticSearch from '../src/clients/elasticsearch.js'
import dotenv from 'dotenv'

dotenv.config()
const elastic = elasticSearch.getInstance()

const INDEX = process.env.INDEX_CONTEXT // ajuste para seu índice
const URL_FIELD = 'url'
const TS_FIELD = '@timestamp'

const DRY_RUN = false
const BATCH_SIZE = 20 // How many URLs to process for deletion in each batch.

/**
 * Finds and deletes duplicate documents using a two-phase approach:
 * 1. Fetch a list of all duplicate URLs using a 'terms' aggregation.
 * 2. Process this list in batches to delete older documents.
 */
async function findAndDeleteDuplicatesReworked() {
  console.log(`Starting reworked duplicate deletion process for index: "${INDEX}"`)
  if (DRY_RUN) {
    console.log('--- DRY RUN MODE IS ENABLED. NO DATA WILL BE DELETED. ---')
  }

  try {
    // Phase 1: Use a 'terms' aggregation to get a definitive list of duplicate URLs.
    // This mirrors the Kibana query that is known to work.
    console.log('Step 1: Fetching list of all duplicate URLs...')
    const aggQuery = {
      index: INDEX,
      size: 0,
      body: {
        aggs: {
          duplicateUrls: {
            terms: {
              field: URL_FIELD,
              min_doc_count: 2,
              // Note: This has a hard limit of 10,000 unique terms.
              // If you have more, you may need to run the script multiple times.
              size: 1000000
            }
          }
        }
      }
    }

    const aggResult = await elastic.search(aggQuery)
    const buckets = aggResult.aggregations.duplicateUrls.buckets

    if (buckets.length === 0) {
      console.log('No duplicate URLs found matching the criteria. Exiting.')
      return
    }

    console.log(`Found a total of ${buckets.length} unique URLs with duplicates.`)
    console.log('Step 2: Starting batch processing for deletion...')

    let totalDeletedCount = 0

    // Phase 2: Process the fetched list of URLs in client-side batches.
    for (let i = 0; i < buckets.length; i += BATCH_SIZE) {
      const batch = buckets.slice(i, i + BATCH_SIZE)
      const batchNum = i / BATCH_SIZE + 1
      console.log(`\n--- Processing Batch #${batchNum} (URLs ${i + 1} to ${i + batch.length}) ---`)

      const idsToDeleteForBatch = []

      for (const bucket of batch) {
        const duplicateUrl = bucket.key
        const docCount = bucket.doc_count

        const searchDupesQuery = {
          index: INDEX,
          _source: false,
          body: {
            query: { term: { [URL_FIELD]: duplicateUrl } },
            sort: [{ [TS_FIELD]: { order: 'desc' } }],
            size: docCount
          }
        }

        const searchResult = await elastic.search(searchDupesQuery)
        const documents = searchResult.hits.hits
        const docsToDelete = documents.slice(1)

        if (docsToDelete.length > 0) {
          idsToDeleteForBatch.push(...docsToDelete.map(doc => doc._id))
        }
      }

      if (idsToDeleteForBatch.length === 0) {
        console.log('No documents to delete in this batch.')
        continue
      }

      // Phase 3: Perform bulk delete for the current batch.
      if (DRY_RUN) {
        console.log(`[DRY RUN] Would delete ${idsToDeleteForBatch.length} documents in this batch.`)
      } else {
        console.log(`Submitting bulk delete for ${idsToDeleteForBatch.length} documents...`)
        const bulkRequestBody = idsToDeleteForBatch.flatMap(id => [
          { delete: { _index: INDEX, _id: id } }
        ])
        const bulkResponse = await elastic.bulk({ refresh: false, body: bulkRequestBody })

        if (bulkResponse.errors) {
          console.error(`Errors encountered during bulk delete for batch #${batchNum}.`)
        } else {
          console.log(
            `Successfully deleted ${idsToDeleteForBatch.length} documents for batch #${batchNum}.`
          )
        }
      }

      totalDeletedCount += idsToDeleteForBatch.length
      console.log(`Total documents processed for deletion so far: ${totalDeletedCount}`)
    }

    console.log('\n--- Process Complete ---')
    console.log(
      `Final summary: ${totalDeletedCount} documents were ${DRY_RUN ? 'identified for deletion' : 'deleted'}.`
    )
  } catch (error) {
    console.error(`\nAn error occurred during the process:`)
    console.error(error)
  }
}

// Execute the function
findAndDeleteDuplicatesReworked().catch(err => {
  console.error('Script execution failed:', err)
})
