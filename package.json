{"name": "sds-context-api", "version": "1.0.0", "description": "Processamento do Contexto do NLP para anúcios", "type": "module", "main": "app.ts", "types": "app.d.ts", "directories": {"test": "test"}, "scripts": {"test": "npm run build:ts && tsc -p test/tsconfig.json && tap --ts \"test/**/*.test.ts\"", "start": "fastify start -l info dist/app.js --options", "start:build": "npm run build:ts && fastify start -l info dist/app.js --options", "start:bull:worker": "npm run build:ts && node dist/bull/Worker.js", "copy:swagger:static": "mkdir -p ./dist/static && cp -r ./node_modules/@fastify/swagger-ui/static/* ./dist/static", "build": "tsc", "build:ts": "tsc", "watch:ts": "tsc -w", "dev": "npm run build:ts && concurrently -k -p \"[{name}]\" -n \"TypeScript,App\" -c \"yellow.bold,cyan.bold\" \"npm:watch:ts\" \"npm:dev:start\"", "dev:worker": "npx tsx src/bull/Worker.ts", "dev:start": "fastify start --ignore-watch=.ts$ -w -l info -P dist/app.js --options"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@elastic/elasticsearch": "^8.19.1", "@extractus/article-extractor": "github:andrem<PERSON>la/article-parser#sds-prod", "@fastify/autoload": "^5.10.0", "@fastify/cors": "^9.0.1", "@fastify/sensible": "^5.6.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.2.0", "@google-cloud/language": "^6.5.0", "@sinclair/typebox": "^0.33.22", "axios": "^1.11.0", "bodybuilder": "^2.5.1", "bullmq": "^5.58.4", "fastify": "^4.29.1", "fastify-cli": "^6.3.0", "fastify-plugin": "^4.5.1", "https-proxy-agent": "^7.0.6", "ioredis": "^5.4.2", "openai": "^5.16.0", "sds-helpers": "github:simpleads/sds-helpers", "tiktoken": "^1.0.22", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^24.3.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "concurrently": "^8.2.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.4", "fastify-tsconfig": "^2.0.0", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}