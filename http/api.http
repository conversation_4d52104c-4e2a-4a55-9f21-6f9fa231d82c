@url = https://tvprime.correiobraziliense.com.br/noticia/277856/celebridades/chega-preocupante-noticia-sobre-o-filho-de-viih-tube-e-eliezer-assessoria-confirma-27112024
@url2 = https://www.tudogostoso.com.br/receita/122852-enroladinho-de-mumia-halloween.html
@url3 = https://tvprime.correiobraziliense.com.br/noticia/271046/celebridades/sensitiva-faz-alerta-preocupante-para-janja-primeira-dama-e-esposa-do-presidente-do-brasil-24102024

### Context API
GET {{API_HOST_URL}}/context?t={{TEST_TOKEN}}&url={{url}}&debug=true

### Content Classification
POST {{API_HOST_URL}}/classy?t={{TEST_TOKEN}}
Content-Type: application/json
{
  "content": "{{url}}",
  "debug": true,
  "elastic": true
}

### Content Classification (Do not try extract article)
### Try to classify only with url
POST {{API_HOST_URL}}/classy?t={{TEST_TOKEN}}
Content-Type: application/json
{
  "debug": true,
  "content": "{{url}}",
  "elastic": false,
  "sourceUrl": true
}

### Article: Extract from URL
POST {{API_HOST_URL}}/article/extract?t={{TEST_TOKEN}}
Content-Type: application/json
{
  "debug": true,
  "url": "{{url}}"
}

### Article: Extract from HTML File
POST {{API_HOST_URL}}/article/extract?t={{TEST_TOKEN}}
Content-Type: application/json
{
  "debug": true,
  "url": "{{url}}",
  "file": "./htmls/article.html"
}

### Arbitrage
POST {{API_HOST_URL}}/arb?t={{TEST_TOKEN}}
Content-Type: application/json
{
  "input": "Chega preocupante notícia sobre o filho de Viih Tube e Eliezer; assessoria confirma",
  "url": "https://tvprime.correiobraziliense.com.br/noticia/277856/celebridades/chega-preocupante-noticia-sobre-o-filho-de-viih-tube-e-eliezer-assessoria-confirma-27112024"
}

### BullMQ Status
GET {{API_HOST_URL}}/bullstats
GET {{API_HOST_URL}}/bullstats/failed

### User Info API
GET {{API_HOST_URL}}/usr?t={{TEST_TOKEN}}