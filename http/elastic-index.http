### Article: Create
PUT {{ES_HOST}}/test_easy_articles
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}
{
  "settings": {
    "index": { "number_of_shards": 4, "number_of_replicas": 0 },
    "analysis": {
      "filter": {
        "ptbr_stopwords":       { "type": "stop", "stopwords": "_brazilian_" },
        "ptbr_stemmer_full":    { "type": "stemmer", "language": "brazilian" },
        "ptbr_stemmer_default": { "type": "stemmer", "name": "minimal_portuguese" }
      },
      "analyzer": {
        "ptbr_default": {
          "tokenizer": "standard",
          "filter": [ "lowercase", "asciifolding", "ptbr_stemmer_default" ]
        },
        "ptbr_full": {
          "tokenizer": "standard",
          "filter": [ "lowercase", "ptbr_stopwords", "ptbr_stemmer_full" ]
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "@timestamp": { "type": "date" },
      "date":       { "type": "date" },
      "domain":     { "type": "keyword", "ignore_above": 512 },
      "type":       { "type": "keyword", "ignore_above": 256 },
      "url":        { "type": "keyword", "ignore_above": 512 },
      "slug":       { "type": "keyword", "ignore_above": 512 },
      "image":      { "type": "keyword", "ignore_above": 512 },
      "title": {
        "type": "text",
        "analyzer": "ptbr_default",
        "fields": {
          "full":    { "type": "text", "analyzer": "ptbr_full" },
          "keyword": { "type": "keyword", "ignore_above": 256 }
        }
      },
      "desc": {
        "type": "text",
        "analyzer": "ptbr_default",
        "fields": {
          "full":    { "type": "text", "analyzer": "ptbr_full" },
          "keyword": { "type": "keyword", "ignore_above": 256 }
        }
      },
      "content": {
        "type": "text",
        "analyzer": "ptbr_default",
        "fields": {
          "full":    { "type": "text", "analyzer": "ptbr_full" },
          "keyword": { "type": "keyword", "ignore_above": 256 }
        }
      }
    }
  }
}


### Context: Create
PUT {{ES_HOST}}/test_easy_contextual
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}
{
  "settings": {
    "index": { "number_of_shards": 4, "number_of_replicas": 0 }
  },
  "mappings": {
    "properties": {
      "@timestamp":      { "type": "date" },
      "domain":          { "type": "keyword", "ignore_above": 256 },
      "url":             { "type": "keyword", "ignore_above": 512 },
      "source":          { "type": "keyword", "ignore_above": 256 },
      "main_categories": {
        "properties": {
          "id":   { "type": "long" },
          "name": { "type": "keyword", "ignore_above": 256 }
        }
      },
      "sub_categories": {
        "properties": {
          "id":   { "type": "long" },
          "name": { "type": "keyword", "ignore_above": 256 }
        }
      },
      "tags": {
        "properties": {
          "id":   { "type": "long" },
          "name": { "type": "keyword", "ignore_above": 256 }
        }
      },
      "raw_categories": {
        "properties": {
          "score": { "type": "float" },
          "name":  { "type": "keyword", "ignore_above": 256 }
        }
      },
      "raw_moderation": {
        "properties": {
          "score": { "type": "float" },
          "name":  { "type": "keyword", "ignore_above": 256 }
        }
      }
    }
  }
}

### Context: Updating mapping
POST {{ES_HOST}}/test_easy_contextual/_mapping
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}
{
  "properties": {
    "raw_moderation": {
        "properties": {
          "score": { "type": "float" },
          "name":  { "type": "keyword", "ignore_above": 256 }
        }
      }
  }
}


### Context: DELETE
DELETE {{ES_HOST}}/test_easy_contextual
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}

### Article: DELETE
DELETE {{ES_HOST}}/test_easy_articles
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}

### Google Policies: Create
PUT {{ES_HOST}}/test_ad_google_policies
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}
{
  "settings": {
    "index": { "number_of_shards": 2, "number_of_replicas": 0 }
  },
  "mappings": {
    "properties": {
      "site":            { "type": "keyword" },
      "url":             { "type": "keyword" },
      "correction":      { "type": "keyword" },
      "problems":        { "type": "keyword" },
      "status":          { "type": "keyword" },
      "property":        { "type": "keyword" },
      "requests":        { "type": "long" },
      "data-complaint":  { "type": "date" },
      "date-last-issue": { "type": "date" }
    }
  }
}


### Google Policies: DELETE
DELETE {{ES_HOST}}/test_ad_google_policies
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}

### Contextual ADServers: Create
PUT {{ES_HOST}}/test_contextual_adservers
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}
{
  "settings": {
    "index": { "number_of_shards": 2, "number_of_replicas": 0 }
  },
  "mappings": {
    "properties": {
      "adserver": { "type": "keyword" },
      "category": { "type": "keyword" },
      "domain":   { "type": "keyword" },
      "url":      { "type": "text", "fields": { "keyword": { "type": "keyword" } } }
    }
  }
}


### Contextual ADServers: DELETE
DELETE {{ES_HOST}}/test_contextual_adservers
Content-Type: application/json
Authorization: Basic {{ES_AUTHORIZATION}}