{
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020
  },
  "env": {
    "node": true,
    "browser": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "complexity": [
      "warn",
      20
    ],
    "prefer-const": "error",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-empty-interface": "off",
    "@typescript-eslint/no-var-requires": "off",
    // "@typescript-eslint/no-unsafe-assignment": "warn",
    // "@typescript-eslint/no-unsafe-member-access": "warn",
    "padding-line-between-statements": [
      "error",
      {
        "blankLine": "any",
        "prev": "*",
        "next": "if"
      },
      {
        "blankLine": "any",
        "prev": "*",
        "next": "block-like"
      },
      {
        "blankLine": "always",
        "prev": "*",
        "next": "block"
      }
    ]
  }
}