{"extends": "fastify-tsconfig", "compilerOptions": {"lib": ["ESNext", "DOM"], "outDir": "dist", "sourceMap": true, "declaration": true, "useUnknownInCatchVariables": false, "noUnusedLocals": false, "noUnusedParameters": false, "isolatedModules": false, "useDefineForClassFields": true, "esModuleInterop": true, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts"], "exclude": ["tsconfig.json"]}