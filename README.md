# SDS CONTEXT API

API Para diversos tipos de contextos: página, usuários, nlp etc...

### DOCUMENTAÇÃO DO FUNCIONAMENTO DO BULLMQ

- fastify.bull.worker()
- fastify.bull.queue()

### Comandos úteis

- Exportar elastic para .json: `curl -k -u user:pass  -X GET"https://localhost:9200/easy_contextual/_search/?size=200000" --header 'Authorization: Basic XXXXXXXX' --header 'Content-Type: application/json' -o context.json`

- Aumentar quantidade (size) que pode ser consultado em uma query do elastic: `curl -k -u user:pass -XPUT "https://localhost:9200/my_index/_settings" -d '{ "index" : { "max_result_window" : 100000 } }' -H "Content-Type: application/json"`

- Limpar documentos deletados (somente se tiver acima de 10% de deletados): `curl -k -u user:pass -XPOST 'https://localhost:9200/my_index/_forcemerge?only_expunge_deletes=true'`

- Forçar Limpar documentos deletados: `curl -k -u user:pass -XPOST 'https://localhost:9200/my_index/_forcemerge?max_num_segments=1'`

### TODO

- [ ] Atualizar unwanted e possiveis outros patterns para estrutura do easy-crawlee
- [ ] Atualizar transformationOptions para normalização no padrão do easy-crawlee
- [ ] Desacoplar Serviço da Context API e UserInfo do Fastify
- [ ] Melhorar tipagem e verificações nos serviços. Tem muito ! espalhado
- [ ] Transformar validateUrlAccess em um Hook ou Plugin
- [ ] Implementar um método melhor de authorization no lugar do token (não atrapalhar o swagger)
- [ ] Criar testes com `bun test`