import { FastifyReply, FastifyRequest, HookHandlerDoneFunction } from 'fastify'
import { ec } from '../utils/helpers.js'
import { GenericInterface } from '../schemas/genericSchema.js'

export function genericLoggerMiddleware(
  req: FastifyRequest<GenericInterface>,
  reply: FastifyReply,
  done: HookHandlerDoneFunction
) {
  const { server: fastify, query, headers, originalUrl } = req

  const url = query.url ? new URL(ec.atob(query.url)) : null
  const uri = url ? url.origin + url.pathname : null
  const from = uri || originalUrl || req.url

  const { 'user-agent': userAgent, 'x-real-ip': xRealIp } = headers
  const ip = xRealIp || req.ip

  fastify.log.info(`Request from: ${from} / ${userAgent} / ${ip}`)

  done()
}
