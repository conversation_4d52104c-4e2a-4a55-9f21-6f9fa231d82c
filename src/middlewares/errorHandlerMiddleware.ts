import fastify, { FastifyError, FastifyReply, FastifyRequest } from 'fastify'

export function errorHandlerMiddleware(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  const statusCode = error.statusCode || 400

  if (error.message.startsWith('querystring')) {
    error.message = 'Invalid querystring.'
  }

  return reply.status(statusCode).send({ error: error.message })
}
