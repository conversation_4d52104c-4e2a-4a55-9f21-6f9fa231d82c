import { FastifyReply, FastifyRequest } from 'fastify'
import integration from '../clients/integration.js'
import { validate as uuidValidate } from 'uuid'
import { ec } from '../utils/helpers.js'
import { GenericInterface } from '../schemas/genericSchema.js'

const validateTokenWithIntegration = async (token: string): Promise<boolean> => {
  try {
    const response = await integration.get<IAuthorizedHost>(`/sites/${token}/valid`)
    return response.data.isValidSite
  } catch (err) {
    return false
  }
}

const checkTokenValidity = async (token: string): Promise<boolean> => {
  const tokenKey = `auth:${token}`
  let isValidToken = await ec.redis.get(tokenKey)

  if (isValidToken === null) {
    isValidToken = await validateTokenWithIntegration(token)
    await ec.redis.set(tokenKey, JSON.stringify(isValidToken))
  }

  return Boolean(isValidToken)
}

export const authorizationMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  const req = request as FastifyRequest<GenericInterface>
  const token = req.query.t ?? null
  const path = req.routeOptions.url

  if (
    (path &&
      ['/bullstats', '/docs', '/article/extract', '/classy'].some(route =>
        path.startsWith(route)
      )) ||
    path === '/'
  ) {
    return
  }

  if (!token || !uuidValidate(token)) {
    return reply.status(500).send({ error: 'Please, provide a valid token' })
  }

  const isValidToken = await checkTokenValidity(token)

  if (!isValidToken) {
    return reply.status(401).send({ statusCode: 401, error: 'Unauthorized site' })
  }
}
