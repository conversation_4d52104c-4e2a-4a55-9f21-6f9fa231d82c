/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/triple-slash-reference */
/* eslint-disable @typescript-eslint/no-unused-vars */

///

/* -------------------------------------------------------------------------
| Tipagem para variáveis de ambiente
------------------------------------------------------------------------- */
declare namespace NodeJS {
  interface ProcessEnv {
    FASTIFY_PORT: string
    FASTIFY_DISABLE_REQ_LOG: string
    API_HOST_URL: string
    UUID_NAMESPACE: string
    SENSITIVE_CAT_SCORE: string
    SENSITIVE_BS_SCORE: string
    SENSITIVE_MODERATION: string

    INDEX_CONTEXT: string
    INDEX_CONTEXT_ADSERVERS: string
    INDEX_GOOGLE_POLICIES: string

    BULL_LOG_DEBUG: string
    BULL_FAIL_LIMIT: string
    BULL_JOB_ATTEMPTS: string
    BULL_JOB_ATTEMPT_DELAYMS: string
    BULL_CONCURRENCY: string

    RD_DB: string
    RD_DB_BULLMQ: string
    RD_PORT: string
    RD_HOST: string
    RD_TTL: string
    RD_TEMP_TTL: string
    RD_AI_TTL: string
    RD_ENABLE: string

    ES_HOST: string
    ES_USER: string
    ES_PASS: string
    ES_AUTHORIZATION: string

    AI_MAX_TOKENS: string
    AI_TEMPERATURE: string
    AI_TOP_P: string
    AI_DEFAULT_MODEL: string

    OPENAI_ORGANIZATION_ID: string
    OPENAI_GENERAL_KEY: string
    OPENAI_GENERAL_MODEL: string
    OPENAI_IMG_SIZE: string

    GEMINI_API_URL: string
    GEMINI_GENERAL_KEY: string
    GEMINI_GENERAL_MODEL: string

    INTEGRATION_PASS: string
    INTEGRATION_URL: string
    INTEGRATION_USER: string

    IP_API_KEY: string
    IP_API_URL: string

    PROXY_ENABLE: string
    PROXY_USER: string
    PROXY_PASS: string
    PROXY_HOST: string
    PROXY_PORT: string

    GOOGLE_CREDENTIALS: string
    GOOGLE_APPLICATION_CREDENTIALS: string
  }
}

/* -------------------------------------------------------------------------
| GERAL
------------------------------------------------------------------------- */
interface IBlocklist {
  default_categories: string[]
  adservers_categories: string[]
  url_keywords: string[]
}

/* -------------------------------------------------------------------------
| Tipagem dos dados do NLP->Classy
------------------------------------------------------------------------- */
interface IClassyItem {
  id: number
  name: string
}

interface IClassyRawItem {
  score: number
  name: string
}

interface IClassyCategoriesData {
  main_categories: IClassyItem[]
  sub_categories: IClassyItem[]
  tags: IClassyItem[]
  raw_categories: IClassyRawItem[]
  '@timestamp': string
}

interface IClassyContextIndexData {
  _id?: string
  url: string
  domain: string
  categories?: IClassyCategoriesData
  moderation?: IClassyRawItem[]
  source: string
}

/* -------------------------------------------------------------------------
| Tipagem dos dados de retorno API Authorization Host
------------------------------------------------------------------------- */
interface IAuthorizedHost {
  isValidSite: boolean
}
