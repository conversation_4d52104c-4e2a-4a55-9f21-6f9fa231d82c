/* -------------------------------------------------------------------------
| Schema para requisições da rota de classificação de texto
------------------------------------------------------------------------- */
import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const classyRequestSchema = Type.Object({
  content: Type.String(),
  elastic: Type.Optional(Type.Boolean()),
  sourceUrl: Type.Optional(Type.Boolean()),
  debug: Type.Optional(Type.Boolean())
})

type ClassyRequestType = Static<typeof classyRequestSchema>

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */
const IClassyRawItem = Type.Array(
  Type.Object({
    score: Type.Number(),
    name: Type.String()
  })
)
const classyResponseSchema = Type.Object({
  runtime: Type.Optional(Type.Number()),
  extract: Type.Optional(Type.String()),
  _id: Type.Optional(Type.String()),
  source: Type.Union([
    Type.Literal('cache'),
    Type.Literal('elastic'),
    Type.Literal('elastic-cache'),
    Type.Literal('article'),
    Type.Literal('article-cache'),
    Type.Literal('url'),
    Type.Literal('url-cache'),
    Type.Literal('content'),
    Type.Literal('content-cache')
  ]),
  categories: Type.Optional(IClassyRawItem),
  moderation: Type.Optional(IClassyRawItem)
})

type ClassyResponseType = Static<typeof classyResponseSchema>
type ClassyRawItemType = Static<typeof IClassyRawItem>

interface ClassyInterface {
  Body: ClassyRequestType
}

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */
const classyOptions: RouteShorthandOptions = {
  schema: {
    tags: ['Content Classification'],
    body: classyRequestSchema,
    response: {
      200: classyResponseSchema
    }
  }
}

export {
  ClassyRequestType,
  ClassyResponseType,
  ClassyRawItemType,
  ClassyInterface,
  classyOptions,
  classyRequestSchema,
  classyResponseSchema
}
