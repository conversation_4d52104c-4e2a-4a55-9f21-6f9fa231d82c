import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const genericRequestSchema = Type.Object({
  url: Type.String(),
  t: Type.String()
})

type GenericRequestType = Static<typeof genericRequestSchema>

interface GenericInterface {
  Querystring: GenericRequestType
}

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */

const genericOptions: RouteShorthandOptions = {
  schema: {
    querystring: genericRequestSchema
  }
}

export { GenericRequestType, GenericInterface, genericOptions }
