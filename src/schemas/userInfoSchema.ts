import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const userInfoRequestSchema = Type.Object({
  t: Type.String()
})

type UserInfoRequestType = Static<typeof userInfoRequestSchema>

interface UserInfoInterface {
  Querystring: UserInfoRequestType
}

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */

const userInfoResponseSchema = Type.Object({
  country: Type.Optional(Type.String()),
  region: Type.Optional(Type.String()),
  city: Type.Optional(Type.String()),
  status: Type.Boolean()
})
type UserInfoResponseType = Static<typeof userInfoResponseSchema>

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */

const userInfoOptions: RouteShorthandOptions = {
  schema: {
    tags: ['User Information'],
    querystring: userInfoRequestSchema,
    response: {
      200: userInfoResponseSchema
    }
  }
}

export { UserInfoRequestType, UserInfoInterface, userInfoOptions, UserInfoResponseType }
