import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const contextRequestSchema = Type.Object({
  url: Type.String(),
  t: Type.String(),
  debug: Type.Optional(Type.Boolean())
})

type ContextRequestType = Static<typeof contextRequestSchema>

interface ContextInterface {
  Querystring: ContextRequestType
}

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */

const contextResponseSchema = Type.Object({
  po: Type.Array(Type.Number()), // policies (direta, badTerms, google, governo)
  bs: Type.Array(Type.Union([Type.Number(), Type.String()])), // brand safety
  ct: Type.Array(Type.Number()), // categories
  er: Type.Optional(Type.Boolean()), // error
  raw_categories: Type.Optional(Type.Array(Type.Any())),
  raw_moderation: Type.Optional(Type.Any())
})
type ContextResponseType = Static<typeof contextResponseSchema>

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */

const contextOptions: RouteShorthandOptions = {
  schema: {
    tags: ['Content Classification'],
    querystring: contextRequestSchema,
    response: {
      200: contextResponseSchema
    }
  }
}

export { ContextRequestType, ContextInterface, contextOptions, ContextResponseType }
