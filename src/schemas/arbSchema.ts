/* -------------------------------------------------------------------------
| Schema para requisições da rota de contextualização de arbitragem
------------------------------------------------------------------------- */
import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const arbRequestSchema = Type.Object({
  input: Type.String(),
  url: Type.String()
})

const arbRequestParamsSchema = Type.Object({
  t: Type.String()
})

type ArbRequestType = Static<typeof arbRequestSchema>
type ArbRequestParamsType = Static<typeof arbRequestParamsSchema>

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */
const arbResponseSchema = Type.Object({
  runtime: Type.Optional(Type.Number()),
  valid: Type.Boolean()
})

type ArbResponseType = Static<typeof arbResponseSchema>

interface ArbInterface {
  Body: ArbRequestType
}

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */
const arbOptions: RouteShorthandOptions = {
  schema: {
    tags: ['Arbitrage'],
    querystring: arbRequestParamsSchema,
    body: arbRequestSchema,
    response: {
      200: arbResponseSchema
    }
  }
}

export {
  ArbRequestType,
  ArbResponseType,
  ArbInterface,
  arbOptions,
  arbRequestSchema,
  arbResponseSchema
}
