/* -------------------------------------------------------------------------
| Schema para requisições da rota de extração de artigos
------------------------------------------------------------------------- */
import { Static, Type } from '@sinclair/typebox'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */
const extractRequestSchema = Type.Object({
  url: Type.String({ format: 'uri' }),
  html: Type.Optional(Type.String()),
  file: Type.Optional(Type.String()),
  debug: Type.Optional(Type.Boolean())
})

type ExtractRequestType = Static<typeof extractRequestSchema>

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */
const extractResponseSchema = Type.Object({
  runtime: Type.Optional(Type.Number()),
  domain: Type.String(),
  url: Type.String(),
  slug: Type.Optional(Type.String()),
  date: Type.Optional(Type.String()),
  ttr: Type.Optional(Type.Number()),
  type: Type.Optional(Type.String()),
  title: Type.String(),
  desc: Type.String(),
  image: Type.Optional(Type.String()),
  images: Type.Optional(Type.Array(Type.String())),
  links: Type.Optional(Type.Array(Type.String())),
  author: Type.Optional(Type.String()),
  content: Type.String()
})

type ExtractResponseType = Static<typeof extractResponseSchema>

interface ExtractInterface {
  Body: ExtractRequestType
}

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */
const extractOptions: RouteShorthandOptions = {
  schema: {
    tags: ['Article Processing'],
    body: extractRequestSchema,
    response: {
      200: extractResponseSchema
    }
  }
}

export {
  ExtractRequestType,
  ExtractResponseType,
  ExtractInterface,
  extractOptions,
  extractRequestSchema,
  extractResponseSchema
}
