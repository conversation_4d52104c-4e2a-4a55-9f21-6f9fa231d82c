import {
  uuid,
  isUuid,
  parseUrl,
  hostname,
  isUrl,
  slug,
  goouri,
  atob,
  btoa,
  sleep,
  redis,
  logger,
  parseFetchErrorMessage
} from 'sds-helpers'

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { get_encoding } from 'tiktoken'

export const __filename = fileURLToPath(import.meta.url)
export const __dirname = path.dirname(__filename)
export const blocklistFile = fs.readFileSync(
  path.resolve(__dirname) + '/../../brandsafety.json',
  'utf8'
)
export const blocklist: IBlocklist = JSON.parse(blocklistFile)

/* -------------------------------------------------------------------------
| Helpers utilizados pela API
------------------------------------------------------------------------- */
export const ec = {
  uuid,
  isUuid,
  parseUrl,
  hostname,
  isUrl,
  slug,
  goouri,
  atob,
  btoa,
  sleep,
  redis,
  logger,
  parseFetchErrorMessage,

  endTime: (startTime: number) => Number(((Date.now() - startTime) / 1000).toFixed(3)),

  /* ---------------------------------------------------------------
  | Tratar Markdown para JSON (Article Create Service)
  --------------------------------------------------------------- */
  parseMarkdownJson(markdownJson: string) {
    const jsonString = markdownJson.replace(/```json\n|\n```/g, '')
    const jsonData = JSON.parse(jsonString)

    return jsonData
  },

  /* -------------------------------------------------------------------------
  | Trazer todos os tipos de url utilizadas para consultas de contexto
  ------------------------------------------------------------------------- */
  contextUrl(url: string) {
    const uri = ec.parseUrl(url)

    if (typeof uri === 'string') {
      throw new Error('Invalid URL Object')
    }

    return {
      ...uri,
      policies: ec.goouri(uri.url),
      contextUid: ec.uuid(ec.slug(uri.url, true), 'context')
    }
  },

  /* -------------------------------------------------------------------------
  | Bloquear URLs com palavras ou caetgorias que podem ser consideradas impróprias
  ------------------------------------------------------------------------- */
  blocklist: blocklist,
  hasBadTerms(url: string, blockedTerms: string[] = blocklist.url_keywords): boolean {
    const urlString = url
      .toLowerCase()
      .split(/[^a-z0-9]+/)
      .join(' ')

    return blockedTerms.some(term => {
      const regex = new RegExp(`\\b${term.replace(/\s+/g, '\\s+')}\\b`, 'i')
      return regex.test(urlString)
    })
  },

  hasSensitiveCategory(rawCat?: IClassyRawItem[]) {
    const defaultMaxScore = Number(process.env.SENSITIVE_CAT_SCORE)
    const blocklistCategories = ec.blocklist.default_categories
    const ADULT_THRESHOLD = 0.1

    const hasSensitiveCategory = rawCat
      ? rawCat.some(cat => {
          const isBlocked = blocklistCategories.some(blocked => cat.name.includes(blocked))
          if (!isBlocked) return false

          // Aplica threshold especial para categorias Adult
          const threshold = cat.name.includes('Adult') ? ADULT_THRESHOLD : defaultMaxScore
          return cat.score > threshold
        })
      : false

    return hasSensitiveCategory
  },

  /* -------------------------------------------------------------------------
  | Contar tokens de um texto baseado no modelo da OpenAI
  | @see: https://platform.openai.com/tokenizer
  ------------------------------------------------------------------------- */
  countTokens(text: string) {
    const encoding = get_encoding('cl100k_base')
    const tokens = encoding.encode(text)
    encoding.free()

    return tokens.length
  },

  /* -------------------------------------------------------------------------
  | Truncar um texto para um número maximo de tokens
  ------------------------------------------------------------------------- */

  truncateToMaxTokens(text: string, maxTokens: number = 700) {
    let truncatedText = ''
    let currentText = ''
    const words = text.split(/\s+/)

    if (ec.countTokens(text) <= maxTokens) {
      return text
    }

    ec.logger.info(`Truncating text to ${maxTokens} tokens...`)

    for (const word of words) {
      const newText = currentText + (currentText ? ' ' : '') + word

      if (ec.countTokens(newText) <= maxTokens) {
        currentText = newText
      } else {
        break
      }
    }

    truncatedText = currentText
    return truncatedText
  }
}
