/* -------------------------------------------------------------------------
| BullMQ Fastify Plugin
------------------------------------------------------------------------- */

import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import fp from 'fastify-plugin'
import { ec } from '../utils/helpers.js'
import { redis } from 'sds-helpers'

function redisPlugin(fastify: FastifyInstance, options: FastifyPluginOptions, done: () => void) {
  const redisInstance = ec.redis.init(Number(process.env.RD_DB))

  fastify.log.info(`Redis Plugin started`)
  fastify.decorate('redis', redisInstance)

  process.on('SIGINT', async () => {
    fastify.log.info('Closing redis connection...')
    redisInstance.disconnect()
  })

  done()
}

// When using .decorate you have to specify added properties for Typescript
declare module 'fastify' {
  export interface FastifyInstance {
    redis: typeof redis
  }
}

export default fp(redisPlugin)
