/* -------------------------------------------------------------------------
| Swagger Fastify Plugin
------------------------------------------------------------------------- */

import { FastifyInstance, FastifyPluginOptions, FastifyRequest } from 'fastify'
import fastifySwagger from '@fastify/swagger'
import fastifySwaggerUi from '@fastify/swagger-ui'
import fp from 'fastify-plugin'
import { GenericInterface } from '../schemas/genericSchema.js'

export default fp(async (fastify, options) => {
  fastify.register(fastifySwagger, {
    swagger: {
      info: {
        title: 'SimpleAds Context API',
        description: 'Testes da API de contexto',
        version: '0.1.0'
      },
      externalDocs: {
        url: 'https://swagger.io',
        description: 'Find more info here'
      },
      host: process.env.API_HOST_URL.replace(/^https?:\/\//, ''),
      consumes: ['application/json'],
      produces: ['application/json']
    }
  })
  fastify.register(fastifySwaggerUi, {
    routePrefix: '/docs',
    theme: {
      title: 'SimpleAds Context API',
      favicon: [
        {
          filename: 'favicon.png',
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          content: Buffer.from(
            '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',
            'base64'
          )
        }
      ]
    },
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false,
      displayRequestDuration: true
    },
    uiHooks: {
      onRequest: function (request, reply, next) {
        // @ts-ignore
        // request.query!.t = process.env.TEST_TOKEN
        // @ts-ignore
        // console.log('onRequest', request.query!.t)
        next()
      },
      preHandler: function (request, reply, next) {
        // @ts-ignore
        // request.query!.t = process.env.TEST_TOKEN
        // @ts-ignore
        // console.log('preHandler', request.query!.t)
        next()
      }
    },
    staticCSP: true,
    transformStaticCSP: header => header,
    transformSpecification: (swaggerObject, request, reply) => {
      return swaggerObject
    },
    transformSpecificationClone: true
  })
})
