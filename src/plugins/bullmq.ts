/* -------------------------------------------------------------------------
| BullMQ Fastify Plugin
------------------------------------------------------------------------- */

import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import fp from 'fastify-plugin'
import BullMQ from '../bull/BullMQ.js'
import { Queue, Worker } from 'bullmq'

function bullMQPlugin(fastify: FastifyInstance, options: FastifyPluginOptions, done: () => void) {
  const bull = new BullMQ('context', fastify)
  // worker.startWorker()

  fastify.log.info(`BullMQ Plugin started: ${bull.workerName}`)

  fastify.decorate('bull', {
    worker: () => bull.getWorker(),
    queue: () => bull.getQueue()
  })

  process.on('SIGINT', async () => {
    fastify.log.info('Closing worker...')
    bull.getWorker()?.close()
  })

  done()
}

// When using .decorate you have to specify added properties for Typescript
declare module 'fastify' {
  export interface FastifyInstance {
    bull: {
      worker: () => Worker | undefined
      queue: () => Queue
    }
  }
}

export default fp(bullMQPlugin)
