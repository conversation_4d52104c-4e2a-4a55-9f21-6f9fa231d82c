import path, { join } from 'path'
import { fileURLToPath } from 'url'
import AutoLoad, { AutoloadPluginOptions } from '@fastify/autoload'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  headerMiddleware,
  errorHandlerMiddleware,
  authorizationMiddleware,
  genericLoggerMiddleware
} from './middlewares/middlewares.js'

export type AppOptions = {
  // opções personalizadas
} & Partial<AutoloadPluginOptions>

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app: FastifyPluginAsync<AppOptions> = async (fastify, opts): Promise<void> => {
  /* -------------------------------------------------------------------------
  | Middlewares
  ------------------------------------------------------------------------- */
  fastify.addHook('preHandler', (r, p) => authorizationMiddleware(r, p))
  fastify.addHook('preHandler', headerMiddleware)
  fastify.addHook('onRequest', genericLoggerMiddleware)

  /* -------------------------------------------------------------------------
  | Error Handler Response
  ------------------------------------------------------------------------- */
  fastify.setErrorHandler((e, r, p) => errorHandlerMiddleware(e, r, p))

  /* -------------------------------------------------------------------------
  | Carregar todos os plugins definidos na pasta ./plugins
  | Plugins de suporte que serão utilizados em toda a aplicação
  ------------------------------------------------------------------------- */
  fastify.register(AutoLoad, {
    dir: join(__dirname, 'plugins'),
    options: opts
  })

  /* -------------------------------------------------------------------------
  | Carregar todas as rotas definidas na pasta ./routes
  ------------------------------------------------------------------------- */
  fastify.register(AutoLoad, {
    dir: join(__dirname, 'routes'),
    options: opts
  })
}

const options = {
  disableRequestLogging: process.env.FASTIFY_DISABLE_REQ_LOG === 'true',
  trustProxy: true,
  logger: {
    serializers: {
      req: (req: FastifyRequest) => {
        return {
          method: req.method,
          url: req.originalUrl,
          hostname: req.hostname,
          remoteAddress: req.headers['x-real-ip'] || req.ip,
          remotePort: req.socket.remotePort
        }
      }
    }
  }
}

export default app
export { app, options }
