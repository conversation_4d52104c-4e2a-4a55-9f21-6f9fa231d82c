/* -------------------------------------------------------------------------
Endpoint para classificação de conteúdo de acordo com o NLP
endpoint POST: /classy?t={{token}}
------------------------------------------------------------------------- */

import { FastifyPluginAsync } from 'fastify'
import ClassyService from '../services/classy/ClassyService.js'
import { ClassyInterface, classyOptions } from '../schemas/classySchema.js'

const classyController: FastifyPluginAsync = async (fastify, options) => {
  const classy = new ClassyService()

  fastify.post<ClassyInterface>('/', classyOptions, async (req, reply) => {
    const data = await classy.run(req.body)
    return reply.send(data)
  })
}

export default classyController
