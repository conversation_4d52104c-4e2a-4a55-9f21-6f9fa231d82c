/* -------------------------------------------------------------------------
Endpoint para visualização do status da fila de processamento
endpoint: /bullstats
------------------------------------------------------------------------- */

import { RouteHandler } from 'fastify'

export const statusMQController: RouteHandler = async (request, reply) => {
  const bull = request.server.bull.queue()
  const counts = await bull.getJobCounts()

  return reply.send(counts)
}

export const failedMQController: RouteHandler = async (request, reply) => {
  const bull = request.server.bull.queue()
  const failed = await bull.getFailed()

  return reply.send(failed)
}
