/* -------------------------------------------------------------------------
Endpoint para identificação de usuário
endpoint: /usr?&t={{token}}
------------------------------------------------------------------------- */

import { FastifyPluginAsync } from 'fastify'
import { UserInfoInterface, userInfoOptions } from '../schemas/userInfoSchema.js'
import { UserInfo } from '../services/userInfo/UserInfo.js'

export const userInfoController: FastifyPluginAsync = async (fastify, options) => {
  fastify.get<UserInfoInterface>('/', userInfoOptions, async (req, reply) => {
    const userInfo = new UserInfo(req, fastify)
    const data = await userInfo.run()
    const ttl = userInfo.cacheTTL()

    reply.header('CDN-Cache-Control', `max-age=${ttl}, must-revalidate`)
    reply.header('Cache-Control', `public, max-age=${ttl}, must-revalidate`)

    return data
  })
}

export default userInfoController
