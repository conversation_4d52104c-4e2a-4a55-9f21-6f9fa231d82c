/* -------------------------------------------------------------------------
Endpoint para validacão de url na blocklist e retorno de contexto
endpoint: /context?url={{b64-url}}&t={{token}}
------------------------------------------------------------------------- */

import { FastifyPluginAsync } from 'fastify'
import { ContextInterface, contextOptions } from '../schemas/contextSchema.js'
import { Context } from '../services/context/ContextService.js'

export const contextController: FastifyPluginAsync = async (fastify, options) => {
  fastify.get<ContextInterface>('/', contextOptions, async (req, reply) => {
    const context = new Context(req, fastify)
    const data = await context.run()
    const ttl = context.cacheTTL()

    reply.header('CDN-Cache-Control', `max-age=${ttl + 120}, must-revalidate`)
    reply.header('Cache-Control', `public, max-age=${ttl >= 7200 ? 7200 : ttl}, must-revalidate`)

    return data
  })
}

export default contextController
