/* -------------------------------------------------------------------------
Endpoint para validacão extração de artigos
endpoint: /article/extract
------------------------------------------------------------------------- */

import { FastifyPluginAsync } from 'fastify'
import { ExtractInterface, extractOptions } from '../schemas/extractSchema.js'
import ExtractService from '../services/extract/ExtractService.js'

const extractController: FastifyPluginAsync = async (fastify, options) => {
  const extract = new ExtractService()

  fastify.post<ExtractInterface>('/', extractOptions, async (req, reply) => {
    const data = await extract.run(req.body)
    return reply.send(data)
  })
}

export default extractController
