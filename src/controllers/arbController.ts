/* -------------------------------------------------------------------------
Endpoint para contextualizção de urls de arbitragem
endpoint: /arb?t={{token}}
------------------------------------------------------------------------- */

import { FastifyPluginAsync } from 'fastify'
import { ArbInterface, arbOptions } from '../schemas/arbSchema.js'
import ArbService from '../services/arb/ArbService.js'

const arbController: FastifyPluginAsync = async (fastify, options) => {
  const arb = new ArbService()

  fastify.post<ArbInterface>('/', arbOptions, async (req, reply) => {
    const data = await arb.run(req.body)
    return reply.send(data)
  })
}

export default arbController
