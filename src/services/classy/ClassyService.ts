/* -------------------------------------------------------------------------
| Serviço para classificação de texto
------------------------------------------------------------------------- */
import {
  ClassyRequestType,
  ClassyResponseType,
  ClassyRawItemType
} from '../../schemas/classySchema.js'
import gClient from '../../clients/google.js'
import { ec } from '../../utils/helpers.js'
import ExtractService from '../extract/ExtractService.js'
import { ContextRepository } from '../../repositories/ElasticRepository.js'
import { ExtractResponseType } from '../../schemas/extractSchema.js'

export default class ClassyService {
  contextRepository: ContextRepository

  constructor() {
    this.contextRepository = new ContextRepository()
  }

  private async fetchGoogleModeration(content: string) {
    const client = gClient()
    const document = { content: content, type: 1, language: 'pt-br' }
    const classification: ClassyRawItemType = []

    const [result] = await client.moderateText({ document })

    if (!result.moderationCategories) {
      return null
    }

    result.moderationCategories.forEach(({ name, confidence }) => {
      if (name && confidence) {
        classification.push({ name: name, score: confidence })
      }
    })

    return classification
  }

  private async fetchGoogleClassification(content: string) {
    const client = gClient()
    const document = { content: content, type: 1, language: 'pt-br' }
    const classificationModelOptions = { v2Model: { contentCategoriesVersion: 2 } }
    const classification: ClassyRawItemType = []

    const [result] = await client.classifyText({
      document,
      classificationModelOptions
    })

    if (!result.categories) {
      return null
    }

    result.categories.forEach(({ name, confidence }) => {
      if (name && confidence) {
        classification.push({ name: name, score: confidence })
      }
    })

    return classification
  }

  /* ---------------------------------------------------------------
  | Para minimizar o custo do google, caso o contexto de categorias já tenha
  | sido classificado pela V1 da API, requisitar somente o brand safety
  --------------------------------------------------------------- */
  private async fetchGoogleContext(content: string, elasticCategories?: IClassyRawItem[]) {
    try {
      const categories = !elasticCategories
        ? await this.fetchGoogleClassification(content)
        : elasticCategories

      const useModeration = process.env.SENSITIVE_MODERATION === 'true'
      const moderation = useModeration ? await this.fetchGoogleModeration(content) : null

      return { categories, moderation }
    } catch (err) {
      ec.logger.yel(`Google Context error: ${err.message}`)
      return { categories: null, moderation: null }
    }
  }

  private async extract(content: string): Promise<ExtractResponseType | undefined> {
    try {
      const extract = new ExtractService()
      const article = await extract.run({ url: content })

      if (article) {
        return article
      }
    } catch (err) {
      ec.logger.yel(`Extract error: ${err.message}, trying URL only...`)
    }

    return undefined
  }

  private trimText(content: string, article?: ExtractResponseType) {
    const text = article ? article.title + '\n' + article.content : content
    return text && text.length > 790 ? text.substring(0, 790) + '...' : text
  }

  /* run classification */
  // eslint-disable-next-line complexity
  public async run({
    content,
    elastic,
    sourceUrl,
    debug
  }: ClassyRequestType): Promise<ClassyResponseType | null> {
    const useModeration = process.env.SENSITIVE_MODERATION === 'true'
    const isUrl = ec.isUrl(content)
    const redisKey = `classy:${ec.uuid(content)}`

    const cache = await ec.redis.get(redisKey)
    if (cache && !debug) return cache

    const startTime = Date.now()
    const extractContent = !sourceUrl ? isUrl : false
    let elasticClassy: (ClassyResponseType & { _id: string }) | undefined

    if (isUrl && elastic) {
      const context = await this.contextRepository.get(content)

      if (context._id) {
        elasticClassy = {
          _id: context._id,
          categories: context.raw_categories,
          moderation: context.raw_moderation,
          source: 'elastic'
        }
        ec.redis.set(redisKey, JSON.stringify({ ...elasticClassy, source: 'elastic-cache' }), 86400)

        const hasRequiredCategories = !useModeration && context.raw_categories
        const hasRequiredModeration = useModeration && context.raw_moderation

        if (hasRequiredCategories || hasRequiredModeration) {
          return {
            ...elasticClassy,
            runtime: debug ? ec.endTime(startTime) : undefined
          }
        }
      }
    }

    /* 800 caracteres é o limite da API do Google */
    const article = extractContent ? await this.extract(content) : undefined
    const text = this.trimText(content, article)
    const { categories, moderation } = await this.fetchGoogleContext(
      text,
      elasticClassy && elasticClassy.categories
    )

    if (useModeration && moderation === null) {
      throw new Error('Failed to fetch Google Moderation')
    }
    if (categories === null) {
      throw new Error('Failed to fetch Google Classification')
    }

    const classification: ClassyResponseType = {
      _id: elasticClassy ? elasticClassy._id : undefined,
      categories: categories,
      moderation: moderation || undefined,
      source: article ? 'article' : isUrl ? 'url' : 'content'
    }
    await ec.redis.set(
      redisKey,
      JSON.stringify({ ...classification, source: `${classification.source}-cache` }),
      86400
    )

    return {
      ...classification,
      extract: debug ? text : undefined,
      runtime: debug ? ec.endTime(startTime) : undefined
    }
  }
}
