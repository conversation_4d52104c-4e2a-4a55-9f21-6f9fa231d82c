/* -------------------------------------------------------------------------
| Serviço para extração de artigos
------------------------------------------------------------------------- */
import sanitizeOptions from './options/sanitizeOptions.js'
import proxyAgent from '../../clients/proxy.js'
import parseOptions from './options/parseOptions.js'
import fetchOptions from './options/fetchOptions.js'
import { transformationOptions } from './options/transformationOptions.js'
import { readFileSync } from 'fs'
import { norm } from './normalize.js'
import { HttpsProxyAgent } from 'https-proxy-agent'
import { ExtractRequestType, ExtractResponseType } from '../../schemas/extractSchema.js'
import { extract, setSanitizeHtmlOptions, addTransformations } from '@extractus/article-extractor'
import { ec } from '../../utils/helpers.js'

export default class ExtractService {
  private proxyAgent: HttpsProxyAgent<string> | undefined

  constructor() {
    this.setupParser()
    this.proxyAgent = proxyAgent.getInstance()
  }

  /* fazer setup de configurações do article-parser */
  private setupParser() {
    addTransformations([transformationOptions])
    setSanitizeHtmlOptions(sanitizeOptions)
  }

  /* extrair artigo com o article-parser */
  private async getArticle(request: ExtractRequestType) {
    try {
      const source = this.getSource(request)
      const article = await extract(source, parseOptions, this.fetchOptions(request))

      if (article) {
        return norm.article(article, request.url)
      }
    } catch (err) {
      const errmsg = ec.parseFetchErrorMessage(err)
      ec.logger.red(`${errmsg} : ${request.url}`)
      throw new Error(errmsg)
    }

    throw new Error('Article not found')
  }

  /* retornar source de acordo com o tipo de input (html, file, url) */
  private getSource({ file, html, url }: ExtractRequestType) {
    return file ? readFileSync(file, 'utf-8') : html ? html : url
  }

  /* habilitar proxy agent caso necessário */
  private fetchOptions({ url, file, html }: ExtractRequestType) {
    if (file || html) {
      return fetchOptions
    }

    const enableProxy = process.env.PROXY_ENABLE === 'true' && !file && !html

    if (enableProxy) {
      fetchOptions.agent = this.proxyAgent
    }

    return fetchOptions
  }

  /* run extract */
  public async run(request: ExtractRequestType): Promise<ExtractResponseType> {
    const startTime = Date.now()
    const article = await this.getArticle(request)

    return {
      runtime: request.debug ? ec.endTime(startTime) : undefined,
      domain: ec.hostname(article.url!),
      url: article.url!,
      type: article.type,
      title: article.title!,
      desc: article.description!,
      date: article.published?.length ? article.published : undefined,
      image: article.image?.length ? article.image : undefined,
      images: article.images?.length ? article.images : undefined,
      content: article.content!
    }
  }
}
