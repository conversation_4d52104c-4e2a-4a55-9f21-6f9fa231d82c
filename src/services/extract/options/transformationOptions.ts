/* -------------------------------------------------------------------------
| Formatação do retorno do conteúdo pelo Article Extractor
------------------------------------------------------------------------- */

import { Transformation } from '@extractus/article-extractor'
import UNWANTED from './unwanted.js'
import { norm } from '../normalize.js'

export const transformationOptions: Transformation = {
  patterns: [/.*/],

  pre: (document: Document) => {
    /* remove tudo que for desnecessário */
    document.querySelectorAll(UNWANTED.ELEMENTS).forEach(element => {
      if (element.parentNode) {
        return element.parentNode.removeChild(element)
      }
    })

    return document
  },

  post: (document: Document) => {
    const elements = document.querySelectorAll('p, div, span, li, h1, h2, h3, h4, h5, h6')
    const lastIndex = elements.length - 1

    elements.forEach((element, index) => {
      /* ignorar mozilla readability element */
      if (element.id.startsWith('readability-page-')) return

      /* remover linhas com termos desnecessários ou vazias */
      /* remover última linha em caso de match com termos desnecessários */
      /* não remover se elemento for vazio mas conter imagem <p><img...></p> */
      const textLower = element.textContent?.toLocaleLowerCase().trim()
      const hasImage = !textLower && !!element.querySelector('img')

      const removeElement =
        !textLower ||
        UNWANTED.ONE_LINE_TERMS.some(term => term === textLower || `${term}:` === textLower) ||
        UNWANTED.START_LINE_TERMS.some(term => textLower.startsWith(term)) ||
        (index === lastIndex && textLower.length <= 70 && norm.removeLastTerms(textLower))

      /* adicionar quebra de linha para o nlp ou remover elemento */
      return removeElement && !hasImage
        ? element.parentNode?.removeChild(element)
        : element.tagName.toLowerCase() !== 'span'
          ? element.prepend('\n')
          : null
    })

    return document
  }
}
