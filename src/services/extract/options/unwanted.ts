export const ELEMENTS = [
  '.about-author',
  '.addthis_tool',
  '.autor-data',
  '.breadcrumb',
  '.breadcrumbs',
  '.cardoso-social',
  '.datebox',
  '.elementor-post-info',
  '.entry-author',
  '.entry-footer',
  '.entry-meta',
  '.mc-column.entities',
  '.newsletter-component',
  '.post-categoria',
  '.post-comment-link',
  '.post-comments',
  '.post-info',
  '.post-labels',
  '.post-read-time',
  '.post-subject',
  '.post-tags',
  '.pt-cv-wrapper',
  '.push-web-notification',
  '.related-posts',
  '.screen-reader-text',
  '.skip-link',
  '.tag-cloud',
  '.tr-caption',
  '.twitter-tweet',
  '.twp-read-time',
  '.twp-single-next-post',
  '.wp-block-embed',
  '.wp-caption-text',
  '.zs-post-meta',
  '[class*=-ads]',
  '[class*="display-none"]',
  '[class*="facebook"]:not([class*="tag-"])',
  '[class*="instagram"]:not([class*="tag-"])',
  '[class*="twitter"]:not([class*="tag-"])',
  '[class*=ads-]',
  '[class*=btn]',
  '[class*=button]',
  '[class*=email]',
  '[class^=ads]',
  '[class^=related-articles]',
  '[data-instgrm-permalink]',
  '[itemprop*="description"]:not(div)',
  '[style*="display: none"]',
  '[style*="display:none"]',
  '[style="display:none"]',
  '#comments',
  '#newsletter',
  '#section-author',
  '#section-tags',
  'aside',
  'button',
  'caption',
  'figcaption',
  'footer',
  'form',
  'iframe',
  'ins',
  'link',
  'nav',
  'noscript',
  'script',
  'style',
  'svg',
  'time',
  // site specific
  '.g-artigo__data-hora', // faroldomaranhao.com.br
  '.heateor_sss_sharing_container', // maranhaodeverdade
  '.hideme', // imperaitz.com
  '.jp-relatedposts', // blogdodanielsantos.com.br
  '.post_report', // noticias.lnove.com.br
  '.post-date', // para blogger (blogdoleandrozebra) (não interfere na extração dos metadados de data)
  '.post-footer', // noticiasubs.blogspot
  '.site-ticker-section', // imaranhao.com.br
  '.td_block_related_posts', // imperaitz.com
  '.td-crumb-container', // imperaitz.com
  '.td-module-meta-info', // imperaitz.com
  '.td-more-articles-box', // imperaitz.com
  '.tdc-footer-wrap ', // imperaitz.com
  '.tdc-header-wrap ', // imperaitz.com
  '.title-post + div.row.align-items-center', // domingos costa
  '[itemprop=datePublished]', // multimidiaonline.com
  '#wpd-post-rating' // blogdamonica,
].join(':not(body),')

export const ONE_LINE_TERMS = [
  'continua após a publicidade..',
  'continua após a publicidade...',
  'continua após a publicidade',
  'veja o vídeo',
  'últimas notícias',
  'mais notícias',
  'veja também',
  'veja mais',
  'veja mais:',
  'publicidade',
  'artigos relacionados',
  'mais lidas',
  'mais comentadas',
  'destaques',
  'comentários',
  'fazer comentário',
  'continue lendo',
  'continue lendo →',
  'da redação/cristiano dias (radialista e jornalista)'
]

export const START_LINE_TERMS = ['leia mais notícias em']

export const LAST_TERMS = ['facebook', 'twitter', 'whatsapp', 'instagram']

export const BANNED_CONTENT = [
  'sign up',
  'deposit bonus',
  'casino bonuses',
  'casino slots',
  'new players',
  'play for free',
  'play free',
  'best online',
  'slot machine',
  'slot games',
  'free spins',
  'free games',
  'bonus rounds',
  'dealer games',
  'pay more',
  'jackpot',
  'unlocked',
  'unlock',
  'unlocking',
  'cracked',
  'booster',
  'enhanced',
  'activator',
  'lorem ipsum',
  'sit amet'
]

export default {
  ELEMENTS,
  ONE_LINE_TERMS,
  START_LINE_TERMS,
  LAST_TERMS,
  BANNED_CONTENT
}
