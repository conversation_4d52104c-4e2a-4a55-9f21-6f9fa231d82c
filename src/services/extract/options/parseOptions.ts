import { ParserOptions } from '@extractus/article-extractor'

const dateSelectors = [
  '.general-metas:has(.fa-calendar-o) li:nth-child(1)',
  'div.noticiaver h2:nth-child(3)', // sistemarosariense.com
  '.post-timestamp', // folhadotrabalho.com.br
  '.small.mb-2.text-muted.text-right' // noca.com.br
].join(',')

const parseOptions: ParserOptions = {
  descriptionLengthThreshold: 60,
  contentLengthThreshold: 60,
  metaData: {
    secondarySelectors: dateSelectors,
    dateLanguage: 'pt-br'
  }
}

export default parseOptions
