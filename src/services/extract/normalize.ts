import UNWANTED from './options/unwanted.js'
import { ec } from '../../utils/helpers.js'
import { ArticleData } from '@extractus/article-extractor'

// eslint-disable-next-line prettier/prettier
const TITLE_SEPARATORS = ["-", "|", "–", "—", "•", "·", "‹", "›", "⁄", "«", "»", "<", ">", ":", "*", "⋆", "~"]
const RE_BLOG_PREFIX = /^Blog do.+?:/i
const RE_BANNED_WORDS = `\\b(${UNWANTED.BANNED_CONTENT.join('|')})\\b`
const RE_NON_LATIN_SCRIPTS = `[\\p{Script=Greek}\\p{Script=Arabic}\\p{Script=Han}\\p{Script=Hebrew}]`
const RE_BAD_CONTENT = new RegExp(`${RE_BANNED_WORDS}|${RE_NON_LATIN_SCRIPTS}`, 'iu')

export const norm = {
  /* ---------------------------------------------------------------
  | Remover última linha em caso de match com termos desnecessários
  --------------------------------------------------------------- */
  removeLastTerms(text: string): boolean {
    return UNWANTED.LAST_TERMS.some(term => {
      const regex = new RegExp(`\\b${term}\\b`)
      return regex.test(text)
    })
  },

  /* ---------------------------------------------------------------
  | Normalizar descrição
  --------------------------------------------------------------- */
  description(string?: string): string | undefined {
    if (string && string.endsWith('Continue lendo →')) {
      return string.slice(0, -'Continue lendo →'.length).trim()
    }

    string = string?.replace(/\n\n+/g, '\n')

    if (string && string.split(' ').length > 55) {
      return string.split(' ').slice(0, 55).join(' ').trim() + '...'
    }

    return string?.trim()
  },

  /* ---------------------------------------------------------------
  | Normalizar título
  --------------------------------------------------------------- */
  title(article: ArticleData): string {
    let newTitle = article.title!.replace(RE_BLOG_PREFIX, '').trim()
    TITLE_SEPARATORS.forEach(separator => {
      if (newTitle.includes(` ${separator} `)) {
        newTitle = newTitle.split(` ${separator} `)[0]
      }
    })

    return newTitle.length <= 20 ? article.title! : newTitle.trim()
  },

  /* ---------------------------------------------------------------
  | Normalizar conteúdo
  | @TODO: Talvez colocar parte dentro do transformationOptions.post
  --------------------------------------------------------------- */
  article(data: ArticleData, reqUrl?: string): ArticleData & { images?: string[] } {
    const contentStripped = data
      .content!.replace(/<img[^>]*\/?>/g, '') // remover todas as imagens do conteúdo
      .replace(/\s*\n\s*/g, '\n') // remover todas as linhas vazias repetidas e espaços
      .replace(/\t/g, '') // remover tabs
      .replace(/<br\s*\/?>/gi, '\n') // substituir <br> por \n
      .trim() // remover espaços em branco no final do conteúdo

    if (data.content && RE_BAD_CONTENT.test(contentStripped)) {
      throw new Error(`Blocked content detected (badwords)`)
    }

    /* encontrar imagens e adicionar no array */
    let imageMatch: RegExpExecArray | null
    const imageSrcRegex = /<img\s+src="([^"]+)"/g
    const images = []

    while ((imageMatch = imageSrcRegex.exec(data.content!)) !== null) {
      try {
        const imageUrl = new URL(imageMatch[1], `https://${data.source}`)

        if (
          imageUrl.hostname === data.source ||
          imageUrl.hostname.endsWith(`.${data.source}`) ||
          imageUrl.hostname.endsWith(`googleusercontent.com`) ||
          imageUrl.hostname.startsWith(`blogger`)
        ) {
          images.push(imageUrl.href)
        }
      } catch (e) {
        /* empty */
      }
    }

    const uri = reqUrl ? ec.contextUrl(reqUrl) : data.url ? ec.contextUrl(data.url) : undefined

    return {
      ...data,
      url: (uri && uri.clean) || data.url,
      title: norm.title(data),
      description: norm.description(data.description),
      content: contentStripped,
      images: images.length > 0 ? images : undefined
    }
  }
}
