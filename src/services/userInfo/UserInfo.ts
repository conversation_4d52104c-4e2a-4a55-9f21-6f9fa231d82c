import { FastifyInstance, FastifyRequest } from 'fastify'
import { UserInfoInterface, UserInfoResponseType } from '../../schemas/userInfoSchema.js'
import ipapi from '../../clients/ipapi.js'

export class UserInfo {
  private req: FastifyRequest<UserInfoInterface>
  private fastify: FastifyInstance
  private redis: FastifyInstance['redis']

  private clientIp: string
  private clientKey: string

  private userInfo: UserInfoResponseType

  constructor(req: FastifyRequest<UserInfoInterface>, fastify: FastifyInstance) {
    this.req = req
    this.fastify = fastify
    this.redis = fastify.redis

    this.clientIp = (this.req.headers['x-real-ip'] as string) || this.req.ip
    this.clientKey = this.clientIp.replaceAll(':', '-')

    this.userInfo = { status: false }
  }

  /* -------------------------------------------------------------------------
  | Handler inicial para processamento do UserInfo
  ------------------------------------------------------------------------- */
  public async run(): Promise<UserInfoResponseType> {
    const cache = await this.redis.get(`user:${this.clientKey}`)

    if (cache === null) {
      this.userInfo = await this.getIpInfo()
      await this.redis.set(`user:${this.clientKey}`, JSON.stringify(this.userInfo), this.cacheTTL())
    }

    return cache || this.userInfo
  }

  /* -------------------------------------------------------------------------
  | Obter informações de IP
  ------------------------------------------------------------------------- */
  private async getIpInfo() {
    if (!this.clientIp) return { status: false }

    try {
      const response = await ipapi.get<{
        countryCode?: string
        region: string
        city: string
      }>(`/${this.clientIp}?key=${process.env.IP_API_KEY}&fields=city,countryCode,region`, {
        responseType: 'json',
        validateStatus: null
      })

      if (response.status === 200 && response.data.countryCode) {
        return {
          country: response.data.countryCode,
          region: response.data.region,
          city: response.data.city,
          status: true
        }
      }
    } catch {
      this.fastify.log.warn(`Error retrieving data from IP: ${this.clientIp}`)
    }

    return { status: false }
  }

  /* -------------------------------------------------------------------------
  | Informar o TTL de cache do Redis de acordo com o contexto
  ------------------------------------------------------------------------- */
  public cacheTTL() {
    return this.userInfo ? 2592000 : undefined
  }
}
