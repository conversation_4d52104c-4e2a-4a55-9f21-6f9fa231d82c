import { FastifyInstance, FastifyRequest } from 'fastify'
import { ec } from '../../utils/helpers.js'
import { ContextInterface, ContextResponseType } from '../../schemas/contextSchema.js'
import { ContextRepository } from '../../repositories/ElasticRepository.js'
import { dictContextSites } from '../../utils/dictSiteCategories.js'
import { Queue } from 'bullmq'
import { BrandSafety } from './BrandSafety.js'

export class Context {
  private req: FastifyRequest<ContextInterface>
  private fastify: FastifyInstance
  private redis: FastifyInstance['redis']
  private bull: Queue

  private uri: ReturnType<typeof ec.contextUrl>
  private debug: boolean

  private clientAgent: string
  private clientIp: string | string[]

  private isAdServerBlocked: boolean
  private isGoogleBlocked: boolean

  private context: ContextResponseType

  constructor(req: FastifyRequest<ContextInterface>, fastify: FastifyInstance) {
    this.req = req
    this.fastify = fastify
    this.redis = fastify.redis
    this.bull = fastify.bull.queue()

    this.uri = ec.contextUrl(ec.atob(this.req.query.url))
    this.debug = Boolean(this.req.query.debug)

    this.clientAgent = this.req.headers['user-agent'] as string
    this.clientIp = this.req.headers['x-real-ip'] || this.req.ip

    this.isAdServerBlocked = false
    this.isGoogleBlocked = false

    this.context = { bs: [99999], ct: [99999], po: [1, 0, 1, 1] }
  }

  /* -------------------------------------------------------------------------
  | Handler inicial para processamento do Contextual
  ------------------------------------------------------------------------- */
  public async run(): Promise<ContextResponseType> {
    const isValidAccess = this.validateUrlAccess()
    const cache = !this.debug && (await this.redis.get(`context:${this.uri.contextUid}`))

    if ((cache === null && isValidAccess) || this.debug) {
      await this.fetchContextData()
      !this.debug &&
        (await this.redis.set(
          `context:${this.uri.contextUid}`,
          JSON.stringify(this.context),
          this.cacheTTL()
        ))
    }

    this.context = cache || this.context

    return this.context
  }

  /* -------------------------------------------------------------------------
  | Informar o TTL de cache do Redis de acordo com o contexto
  ------------------------------------------------------------------------- */
  public cacheTTL() {
    const ttl =
      this.context.ct[0] === 99999 || this.context.er ? process.env.RD_TEMP_TTL : process.env.RD_TTL
    return parseInt(ttl)
  }

  /* -------------------------------------------------------------------------
  | Retornar e(ou) indexar dados do Contexto e da Blocklist via NLP/Elastic
  ------------------------------------------------------------------------- */
  private async fetchContextData() {
    // @TODO: SOLUÇÃO TEMPORÁRIA PARA SITES COM CATEGORIAS ESTÁTICAS
    const domain = ec.hostname(this.uri.clean)

    if (dictContextSites[domain]) {
      return (this.context.ct = dictContextSites[domain])
    }

    try {
      const context = await new ContextRepository().get(this.uri.url)

      this.isAdServerBlocked = context.isADServerBlocked
      this.isGoogleBlocked = context.isGoogleBlocked

      if (context.cat) {
        this.setBrandSafety(context.raw_categories, context.raw_moderation)
        this.setPolicies(context.raw_categories)
        this.setCategories(context.cat)

        this.context.raw_categories = this.debug ? context.raw_categories : undefined
        this.context.raw_moderation = this.debug ? context.raw_moderation : undefined

        const useModeration = process.env.SENSITIVE_MODERATION === 'true'
        const hasRequiredCategories = !useModeration && context.raw_categories
        const hasRequiredModeration = useModeration && context.raw_moderation

        if (hasRequiredCategories || hasRequiredModeration) {
          return this.context
        }
      }
    } catch (err) {
      this.fastify.log.warn(`Check elastic context error: ${err.message}`)
      this.context.er = true
      return this.context
    }

    return this.analyzeContext()
  }

  /* -------------------------------------------------------------------------
  | NLP: processar verificação de contexto
  ------------------------------------------------------------------------- */
  private async analyzeContext() {
    try {
      this.bull.add('context', { url: this.uri.clean }, { jobId: this.uri.contextUid })
    } catch (err) {
      this.fastify.log.info(`Context Queue Fail: ${this.uri.clean}`)
    }
  }

  /* -------------------------------------------------------------------------
  | Setar e retornar categorias formatadas:
  ------------------------------------------------------------------------- */
  private setCategories(categories: IClassyItem[]) {
    if (categories.length) {
      this.context.ct = categories.map(cat => cat.id)
    }
  }

  /* ---------------------------------------------------------------
  | Setar Brand Safety
  --------------------------------------------------------------- */
  private setBrandSafety(rawCat?: IClassyRawItem[], rawModeration?: IClassyRawItem[]) {
    const bs = new BrandSafety(rawCat, rawModeration)
    const bsTopicsIds = rawModeration || rawCat ? bs.getIds() : undefined

    this.context.bs = bsTopicsIds || this.context.bs
  }

  /* -------------------------------------------------------------------------
  | Retornar policies em ordem: (depreciado)
  | Direta, Bad Terms, Google, Governo
  ------------------------------------------------------------------------- */
  private setPolicies(rawCat?: IClassyRawItem[]) {
    const hasBadTerms = ec.hasBadTerms(this.uri.url)
    const hasSensitiveCategory = ec.hasSensitiveCategory(rawCat)

    /* bloqueio de campanhas em geral */
    const isCampaignBlocked = hasBadTerms || hasSensitiveCategory

    /* bloqueio de campanhas do governo (enable_gov) */
    const isGovBlocked = this.isAdServerBlocked || isCampaignBlocked

    /* setar policies */
    this.context.po = [
      Number(!isCampaignBlocked),
      Number(!hasBadTerms),
      Number(!this.isGoogleBlocked),
      Number(!isGovBlocked)
    ]
  }

  /* ------------------------------------------------------------------------------
  | Verificar e realizar logs se o request para a url está apto para processamento
  ------------------------------------------------------------------------------- */
  private validateUrlAccess() {
    const urlPattern = /(\/|\?|&)(author|search|busca|wp-content|cache)[/|?|=|&]/
    const agentPattern = /(headlesschrome|bot|spider|bing|google|yandex|petalbot|bingbot)/gi

    const isGoogleAppsScript = /Google-Apps-Script/i.test(this.clientAgent)
    const isUrlDenied = urlPattern.test(this.uri.url)
    const isAgentDenied = !isGoogleAppsScript && agentPattern.test(this.clientAgent)

    if (isAgentDenied) {
      this.fastify.log.info(
        `Bot identified: ${this.uri.clean} / ${this.clientAgent} / ${this.clientIp}`
      )
      throw new Error(`${this.clientAgent} identified`)
    }

    if (isUrlDenied) {
      this.fastify.log.info(`Pagination or Search Page identified: ${this.uri.clean}`)
    }

    return !isUrlDenied && !isAgentDenied
  }
}
