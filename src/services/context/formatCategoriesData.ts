/* -------------------------------------------------------------------------
| Formatar dados do NLP->Classy para indexação no Elastic
------------------------------------------------------------------------- */

import { dictContextCategories } from '../../utils/dictContextCategories.js'

export default function formatCategoriesData(rawData: IClassyRawItem[]): IClassyCategoriesData {
  const mainCategories: IClassyItem[] = []
  const subCategories: IClassyItem[] = []
  const tags: IClassyItem[] = []
  const rawCategories = rawData.map(item => item.name.split('/').filter(n => n))

  // eslint-disable-next-line complexity
  rawCategories.forEach(cat => {
    /* adicionar categorias principais "cat[0]" e evitar repetição */
    if (cat[0] && cat[0] !== 'Other') {
      const catID = Object.keys(dictContextCategories).find(
        key => cat[0] === dictContextCategories[parseInt(key)]
      )
      catID &&
        !mainCategories.some(cat => cat.id === +catID) &&
        mainCategories.push({ id: +catID, name: dictContextCategories[parseInt(catID)] })
    }

    /* adicionar sub categorias "cat[1]" e evitar repetição */
    if (cat[1] && cat[1] !== 'Other') {
      const subCatID = Object.keys(dictContextCategories).find(
        key => cat[1] === dictContextCategories[parseInt(key)]
      )
      subCatID &&
        !subCategories.some(cat => cat.id === +subCatID) &&
        subCategories.push({ id: +subCatID, name: dictContextCategories[parseInt(subCatID)] })
    }

    /* adicionar tags level 1 "cat[2]" e evitar repetição */
    if (cat[2] && cat[2] !== 'Other') {
      const childCatID = Object.keys(dictContextCategories).find(
        key => cat[2] === dictContextCategories[parseInt(key)]
      )
      childCatID &&
        !tags.some(cat => cat.id === +childCatID) &&
        tags.push({ id: +childCatID, name: dictContextCategories[parseInt(childCatID)] })
    }

    /* adicionar tags level 2 "cat[3]" e evitar repetição */
    if (cat[3] && cat[3] !== 'Other') {
      const grandChildCatID = Object.keys(dictContextCategories).find(
        key => cat[3] === dictContextCategories[parseInt(key)]
      )
      grandChildCatID &&
        !tags.some(cat => cat.id === +grandChildCatID) &&
        tags.push({ id: +grandChildCatID, name: dictContextCategories[parseInt(grandChildCatID)] })
    }
  })

  return {
    main_categories: mainCategories,
    sub_categories: subCategories,
    tags: tags,
    raw_categories: rawData,
    '@timestamp': new Date().toISOString()
  }
}
