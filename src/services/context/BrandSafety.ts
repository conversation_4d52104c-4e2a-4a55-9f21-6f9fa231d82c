import { brandSafetyTopics } from '../../utils/dictContextCategories.js'

export class BrandSafety {
  private readonly maxScore: number
  private readonly nameToIdMap: Map<string, number>
  private readonly validTopicsSet: Set<string>
  private readonly filteredTopics: IClassyRawItem[] | null

  /**
   * Construtor da classe BrandSafety.
   * @param rawCat Lista de itens brutos da categoria.
   * @param rawModeration Lista de itens brutos da moderação.
   */
  constructor(rawCat: IClassyRawItem[] = [], rawModeration: IClassyRawItem[] = []) {
    this.maxScore = Number(process.env.SENSITIVE_BS_SCORE)
    this.nameToIdMap = new Map<string, number>()
    this.validTopicsSet = new Set<string>()

    for (const [id, topics] of Object.entries(brandSafetyTopics)) {
      const numericId = Number(id)
      if (!isNaN(numericId)) {
        topics.forEach(topic => {
          this.nameToIdMap.set(topic, numericId)
          this.validTopicsSet.add(topic)
        })
      }
    }

    // Processa os tópicos filtrados no construtor
    this.filteredTopics = this.filterTopics(rawCat, rawModeration)
  }

  /**
   * Filtra os tópicos de segurança de marca com base na pontuação máxima e nos tópicos válidos.
   * @param rawCat Lista de itens brutos da categoria.
   * @param rawModeration Lista de itens brutos da moderação.
   * @returns Array de itens filtrados ou null se nenhum tópico atender aos critérios.
   */
  private filterTopics(
    rawCat: IClassyRawItem[],
    rawModeration: IClassyRawItem[]
  ): IClassyRawItem[] | null {
    const allTopics = [...rawCat, ...rawModeration].filter(
      topic => topic.score >= this.maxScore && this.validTopicsSet.has(topic.name)
    )

    return allTopics.length > 0 ? allTopics : null
  }

  /**
   * Obtém os tópicos filtrados de segurança de marca.
   * @returns Array de itens filtrados ou null se nenhum tópico atender aos critérios.
   */
  public getTopics(): IClassyRawItem[] | null {
    return this.filteredTopics
  }

  /**
   * Obtém os IDs de segurança de marca com base nos tópicos filtrados.
   * @returns Array de IDs correspondentes ou [0] se nenhum ID for encontrado.
   */
  public getIds(): number[] {
    if (!this.filteredTopics) return [0]

    const ids = new Set<number>()

    for (const topic of this.filteredTopics) {
      const id = this.nameToIdMap.get(topic.name)
      if (id !== undefined) {
        ids.add(id)
      }
    }

    return ids.size > 0 ? Array.from(ids) : [0]
  }
}
