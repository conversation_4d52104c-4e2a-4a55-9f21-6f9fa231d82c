/* -------------------------------------------------------------------------
| Serviço para contextualização de arbitragem
------------------------------------------------------------------------- */
import { ec } from '../../utils/helpers.js'
import { openai } from '../../clients/openai.js'
import { ArbRequestType, ArbResponseType } from '../../schemas/arbSchema.js'

export default class ArbService {
  constructor() {}

  public async run(request: ArbRequestType): Promise<ArbResponseType> {
    const startTime = Date.now()

    console.log(request.url)
    console.log(request.input)

    const { response, result, error } = await openai.generate({
      instructions: `Decide if the following content is valid for arbitrage. If it is, return "true". If it is not, return "false"`,
      input: `Content: ${request.url} for ${request.input}`
    })

    console.log('response', response)
    // @ts-ignore
    console.log('response text', response?.output_text)
    console.log('result', result)
    console.log('error', error)

    return {
      runtime: ec.endTime(startTime),
      valid: Boolean(result)
    }
  }
}
