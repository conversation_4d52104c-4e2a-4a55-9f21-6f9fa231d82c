import fp from 'fastify-plugin'
import { FastifyPluginAsync } from 'fastify'
import { failedMQController, statusMQController } from '../controllers/statusMQController.js'
import contextController from '../controllers/contextController.js'
import classyController from '../controllers/classyController.js'
import userInfoController from '../controllers/userInfoController.js'
import articleExtractController from '../controllers/articleExtractController.js'
import arbController from '../controllers/arbController.js'

const routes: FastifyPluginAsync = async (fastify, options) => {
  // Rota para inputar/pegar informações do usuário
  fastify.register(userInfoController, { prefix: '/usr' })

  // Rota para inputar/pegar informações de contexto de uma URL
  fastify.register(contextController, { prefix: '/context' })

  // Rota para inputar/pegar extração de artigos na fila
  fastify.register(articleExtractController, { prefix: '/article/extract' })

  // Rota para classificação de conteúdo
  fastify.register(classyController, { prefix: '/classy' })

  // Rota para contextualização de url de arbitragem
  fastify.register(arbController, { prefix: '/arb' })

  // Rotas para verificar o status da fila
  fastify.get('/bullstats', { schema: { tags: ['Stats'] } }, statusMQController)
  fastify.get('/bullstats/failed', { schema: { tags: ['Stats'] } }, failedMQController)

  // Rota para verificar se o serviço está online
  fastify.get('/', { schema: { tags: ['Stats'] } }, (req, reply) =>
    reply.send({ status: 'online' })
  )
}

export default fp(routes)
