/* -------------------------------------------------------------------------
| Retornar e(ou) indexar dados do Contexto e da Blocklist via NLP/Elastic
| @TODO: Tipagem do ElasticSearch .hits.hits
| @TODO: Verificação de URL indiferente com ou sem trailing slash !IMPORTANT
------------------------------------------------------------------------- */
import bodybuilder from 'bodybuilder'
import elasticSearch from '../clients/elasticsearch.js'
import { ec } from '../utils/helpers.js'
const elastic = elasticSearch.getInstance()

export class ContextRepository {
  public async get(url: string) {
    const uri = ec.contextUrl(url)

    try {
      const bodyQueryADServersBlocklist = bodybuilder()
        .rawOption('_source', ['url', 'adserver', 'category'])
        .size(1)
        .query('match_phrase', 'url.keyword', uri.clean.replace(/\/$/, ''))
        .query('terms', 'category', ec.blocklist.adservers_categories)
        .build()

      const bodyQueryGooglePolicies = bodybuilder()
        .rawOption('_source', ['url'])
        .size(1)
        .filter('term', 'url', uri.policies)
        .filter('term', 'correction', 'Sim')
        .build()

      const bodyQueryContext = bodybuilder()
        .rawOption('_source', ['url', 'main_categories', 'raw_categories', 'raw_moderation'])
        .sort('@timestamp', 'desc')
        .size(1)
        .query('term', 'url', uri.clean)
        .build()

      const body = await elastic.msearch({
        searches: [
          { index: process.env.INDEX_CONTEXT_ADSERVERS },
          { ...bodyQueryADServersBlocklist },
          { index: process.env.INDEX_GOOGLE_POLICIES },
          { ...bodyQueryGooglePolicies },
          { index: process.env.INDEX_CONTEXT },
          { ...bodyQueryContext }
        ]
      })

      const [adServerPolicies, googlePolicies, contextResponse] = body.responses

      // @ts-ignore
      const contextHits = contextResponse.hits.hits
      const hasHits = contextHits.length > 0
      const context = hasHits ? contextHits[0]._source : null

      return {
        _id: context ? (contextHits[0]._id as string) : undefined,
        cat: context ? (context.main_categories as IClassyItem[]) : undefined,
        // subcat: context ? (context.sub_categories as IClassyItem[]) : undefined,
        // tags: context ? (context.tags as IClassyItem[]) : undefined,
        raw_categories: context ? (context.raw_categories as IClassyRawItem[]) : undefined,
        raw_moderation: context ? (context.raw_moderation as IClassyRawItem[]) : undefined,
        // @ts-ignore
        isADServerBlocked: Boolean(adServerPolicies.hits.hits.length),
        // @ts-ignore
        isGoogleBlocked: Boolean(googlePolicies.hits.hits.length)
      }
    } catch (err) {
      throw new Error('Elasticsearch: Some error when get data from elastic')
    }
  }

  /* ---------------------------------------------------------------
  | Caso o contexto de categorias já tenha sido indexado,
  | realizar update do documento com o brand safety
  --------------------------------------------------------------- */
  public async index(data: IClassyContextIndexData): Promise<boolean> {
    const index = process.env.INDEX_CONTEXT
    const document = {
      source: data.source,
      url: data.url,
      domain: data.domain,
      raw_moderation: data.moderation,
      ...data.categories
    }

    try {
      let result
      if (data._id) {
        result = await elastic.update({
          index,
          id: data._id,
          doc: document,
          doc_as_upsert: true
        })
        if (result.result !== 'updated' && result.result !== 'noop') {
          return false
        }
      } else {
        result = await elastic.index({ index, document })
        if (result.result !== 'created') {
          return false
        }
      }

      return true
    } catch (error) {
      return false
    }
  }
}
