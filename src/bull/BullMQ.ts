/* -------------------------------------------------------------------------
| Worker e Queue do BullMQ para a context api
------------------------------------------------------------------------- */
import { Worker, Job, WorkerOptions, Queue, DefaultJobOptions } from 'bullmq'
import { FastifyInstance } from 'fastify'
import ClassyService from '../services/classy/ClassyService.js'
import { ec } from '../utils/helpers.js'
import { ContextRepository } from '../repositories/ElasticRepository.js'
import formatCategoriesData from '../services/context/formatCategoriesData.js'

export class BullMQ {
  private worker: Worker | undefined
  private queue: Queue | undefined
  private workerOpts: WorkerOptions
  private defaultJobOptions: DefaultJobOptions | undefined

  constructor(
    public workerName = 'context',
    private fastify?: FastifyInstance,
    initQueue = true
  ) {
    this.worker = undefined
    this.workerOpts = {
      concurrency: Number(process.env.BULL_CONCURRENCY),
      removeOnFail: { count: Number(process.env.BULL_FAIL_LIMIT) },
      connection: {
        host: process.env.RD_HOST,
        port: Number(process.env.RD_PORT),
        db: Number(process.env.RD_DB_BULLMQ)
      }
    }
    this.defaultJobOptions = {
      removeOnComplete: true,
      attempts: Number(process.env.BULL_JOB_ATTEMPTS),
      backoff: {
        type: 'exponential',
        delay: Number(process.env.BULL_JOB_ATTEMPT_DELAYMS)
      }
    }

    initQueue ? this.startQueue() : undefined
  }

  public getWorker = () => this.worker
  public getQueue = () => (!this.queue ? this.startQueue() : this.queue)

  public startWorker() {
    this.worker = new Worker(this.workerName, async job => this.handleJob(job), this.workerOpts)
    this.addListeners()
  }

  public startQueue() {
    this.queue = new Queue(this.workerName, {
      defaultJobOptions: this.defaultJobOptions,
      connection: this.workerOpts.connection
    })

    return this.queue
  }

  private addListeners() {
    if (!this.worker) return

    this.worker.on('completed', async job => {
      if (job.returnvalue?.error) {
        return this.warn(`Job ignored: ${job.id} / ${job.data.url} -> ${job.returnvalue.error}`)
      }

      if (process.env.BULL_LOG_DEBUG === 'true') {
        this.log(`Job completed: ${job.id} / ${job.data.url}`)
      }
    })

    this.worker.on('stalled', (jobId, prev) => {
      this.log(`Job stalled: ${jobId} -> ${prev}`)
    })

    this.worker.on('failed', (job, err) => {
      this.warn(`Job failed: ${job?.id} / ${job?.data.url} -> ${err.message}`)
    })

    this.worker.on('error', error => {
      this.log(`Job error: ${error}`)
    })
  }

  private async handleJob(job: Job) {
    if (job.name === 'context') {
      const context = await new ClassyService().run({
        content: job.data.url,
        elastic: true,
        sourceUrl: false
      })

      if (context && context.categories) {
        const uri = ec.contextUrl(job.data.url)
        const categories = formatCategoriesData(context.categories)
        const indexData = {
          _id: context._id,
          url: uri.clean,
          domain: uri.hostname,
          moderation: context.moderation,
          categories: categories,
          source: context.source
        }

        await new ContextRepository().index(indexData)
      }

      return
    }
  }

  private log(message: string) {
    if (this.fastify) {
      return this.fastify.log.info(message)
    }

    return ec.logger.info(message)
  }

  private warn(message: string) {
    if (this.fastify) {
      return this.fastify.log.warn(message)
    }

    return ec.logger.yel(message)
  }
}

function returnErrorRandomly() {
  if (Math.random() > 0.5) {
    throw new Error('Some random Error')
  } else {
    return true
  }
}

export default BullMQ
