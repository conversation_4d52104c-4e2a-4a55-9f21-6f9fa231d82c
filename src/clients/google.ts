import language, { LanguageServiceClient } from '@google-cloud/language'
import * as path from 'path'
import { __dirname, ec } from '../utils/helpers.js'
import dotenv from 'dotenv'
dotenv.config()

function gClient(): LanguageServiceClient {
  const credentials = process.env.GOOGLE_CREDENTIALS.split(',')
  const sa = credentials[Math.floor(Math.random() * credentials.length)]
  process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, `../../credentials/${sa}`)

  // ec.logger.info('Classy Google SA:', sa)

  const client = new language.LanguageServiceClient()
  return client
}

export default gClient
