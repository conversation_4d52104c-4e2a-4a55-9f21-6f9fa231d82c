import OpenAI from 'openai'
import dotenv from 'dotenv'
import { ResponseCreateParamsBase } from 'openai/resources/responses/responses.js'

dotenv.config()

export const DEFAULT_OPTIONS: ResponseCreateParamsBase = {
  model: process.env.OPENAI_GENERAL_MODEL,
  // temperature: Number(process.env.AI_TEMPERATURE),
  max_output_tokens: Number(process.env.AI_MAX_TOKENS),
  top_p: Number(process.env.AI_TOP_P),
  reasoning: {
    effort: 'minimal',
    summary: null
  },
  text: {
    format: {
      type: 'text'
    },
    verbosity: 'low'
  },
  instructions: undefined,
  input: undefined
}

export const openai = {
  client: new OpenAI({
    organization: process.env.OPENAI_ORGANIZATION_ID,
    apiKey: process.env.OPENAI_GENERAL_KEY
  }),
  generate: async ({
    input,
    instructions,
    model
  }: {
    model?: string
    instructions?: string
    input: string
  }) => {
    try {
      const options = { ...DEFAULT_OPTIONS, input }
      if (model) options.model = model
      if (instructions) options.instructions = instructions

      const response = await openai.client.responses.create(options)

      return { response, result: response._request_id, error: undefined }
    } catch (err) {
      return { response: undefined, result: undefined, error: err.message }
    }
  }
}
