import { HttpsProxyAgent } from 'https-proxy-agent'
import { ec } from '../utils/helpers.js'
import dotenv from 'dotenv'
dotenv.config()

const proxyAgent = (() => {
  let instance: HttpsProxyAgent<string> | undefined

  function getInstance() {
    if (!instance && process.env.PROXY_ENABLE === 'true') {
      const proxyHost = process.env.PROXY_HOST.split(',')
      const randomHost = proxyHost[Math.floor(Math.random() * proxyHost.length)]
      const proxyAuth = `${process.env.PROXY_USER}:${process.env.PROXY_PASS}`
      const proxyUrl = `https://${proxyAuth}@${randomHost}:${process.env.PROXY_PORT}`

      ec.logger.info(`Proxy enabled:`, randomHost)

      instance = new HttpsProxyAgent(proxyUrl)
    }

    return instance
  }

  return {
    getInstance
  }
})()

export default proxyAgent
