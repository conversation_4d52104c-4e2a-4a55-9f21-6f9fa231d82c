import { Client } from '@elastic/elasticsearch'
import dotenv from 'dotenv'
dotenv.config()

const elasticSearch = (() => {
  let instance: Client

  function getInstance() {
    if (!instance) {
      instance = new Client({
        node: process.env.ES_HOST,
        auth: {
          username: process.env.ES_USER,
          password: process.env.ES_PASS
        }
      })
    }

    return instance
  }

  return {
    getInstance
  }
})()

export default elasticSearch
