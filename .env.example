FASTIFY_PORT=3000
FASTIFY_ADDRESS=0.0.0.0
FASTIFY_DISABLE_REQ_LOG=true
API_HOST_URL=http://localhost:3000
UUID_NAMESPACE=66262b8f-3c6c-4b3a-8cda-a77d2dbd1321
SENSITIVE_CAT_SCORE=0.5
SENSITIVE_BS_SCORE=0.5
SENSITIVE_MODERATION=true

# Indexes
# INDEX_ARTICLES=easy_articles # deprecated
INDEX_CONTEXT=easy_contextual2
INDEX_CONTEXT_ADSERVERS=contextual_adservers
INDEX_GOOGLE_POLICIES=ad_google_policies

# BullMQ
BULL_LOG_DEBUG=true
BULL_FAIL_LIMIT=100000
BULL_JOB_ATTEMPTS=100
BULL_JOB_ATTEMPT_DELAYMS=1000
BULL_CONCURRENCY=5

# Redis Config
RD_DB=1
RD_DB_BULLMQ=2
RD_PORT=6379
RD_HOST=127.0.0.1
RD_TTL=84600 # 24 HORAS
RD_TEMP_TTL=5 # 600 [10 MINUTOS]
RD_AI_TTL=2592000 # 30 DIAS
RD_ENABLE=true

# Elastic Config
ES_HOST=#
ES_USER=#
ES_PASS=#
ES_AUTHORIZATION = "XXXXXXXXXX"

# AI Config
AI_MAX_TOKENS=5000
AI_TEMPERATURE=0.5
AI_TOP_P=1
AI_DEFAULT_MODEL=gemini-2.5-flash

# OpenAI
OPENAI_ORGANIZATION_ID=#
OPENAI_GENERAL_KEY=#
OPENAI_GENERAL_MODEL=gpt-5-nano
OPENAI_IMG_SIZE=1024x1024

# Google
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai/
GEMINI_GENERAL_KEY=#
GEMINI_GENERAL_MODEL=gemini-2.5-flash-lite

# Integration Host Authorization
INTEGRATION_URL=https://integration.sds.tec.br
INTEGRATION_USER=#
INTEGRATION_PASS=#

# PRO IP-API
IP_API_KEY=#
IP_API_URL=https://pro.ip-api.com/json

# Proxy
PROXY_ENABLE=true
PROXY_USER=#
PROXY_PASS=#
PROXY_HOST=host1.domain.tld,host2.domain.tld
PROXY_PORT=89

# Google
GOOGLE_CREDENTIALS=sa.json,sa2.json

# Tests
TEST_TOKEN=#